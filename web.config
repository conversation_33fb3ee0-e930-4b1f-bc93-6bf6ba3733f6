<?xml version="1.0" encoding="UTF-8"?>
<configuration>

<system.webServer>

    <httpErrors errorMode="Detailed" />
    <asp scriptErrorSentToBrowser="true"/>

    <rewrite>
    <rules>
        <rule name="RuleRemoveIndex" stopProcessing="true">
            <match url="^(.*)$" ignoreCase="false" />
            <conditions>
                <add input="{REQUEST_FILENAME}" matchType="IsFile" ignoreCase="false" negate="true" />
                <add input="{REQUEST_FILENAME}" matchType="IsDirectory" ignoreCase="false" negate="true" />
            </conditions>
            <action type="Rewrite" url="index.php/{R:1}" appendQueryString="true"/>
        </rule>
    </rules>
    </rewrite>

</system.webServer>

<system.web>
    <customErrors mode="Off"/>
    <compilation debug="true"/>
</system.web>

</configuration>